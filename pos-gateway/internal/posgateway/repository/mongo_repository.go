package repository

import (
	"context"
	"time"

	"github.com/nutanalabs/pos-gateway/internal/config"
	"github.com/nutanalabs/pos-gateway/internal/constants"
	logger "github.com/roppenlabs/rapido-logger-go"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

type MongoRepository interface {
	PushPOSMenu(bson.M, map[string]interface{}, string) error
	PushPOSOrder(bson.M, map[string]interface{}, string) error
}

type mongoRepositoryImpl struct {
	mongoClient *mongo.Database
	config      *config.Config
}

func NewMongoRepository(mongoClient *mongo.Client, config *config.Config) MongoRepository {
	client := mongoClient.Database(config.Mongo.Database)
	return &mongoRepositoryImpl{
		mongoClient: client,
		config:      config,
	}
}

func (m mongoRepositoryImpl) PushPOSMenu(filter bson.M, menu map[string]interface{}, provider string) error {
	// Create a context with timeout
	timeout := time.Duration(30) * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// Start a session for the transaction
	session, err := m.mongoClient.Client().StartSession()
	if err != nil {
		logger.Error(logger.Format{
			Message: "Failed to start MongoDB session",
			Data: map[string]string{
				"error": err.Error(),
			},
		})
		return err
	}
	defer session.EndSession(ctx)

	// Execute the transaction
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		// 1. Delete existing menu and items
		posMenuCollection := m.mongoClient.Collection(constants.POSMenuCollection)
		// Delete menu
		_, err := posMenuCollection.DeleteMany(sessCtx, filter)
		if err != nil {
			logger.Error(logger.Format{
				Message: "Failed to delete existing menu",
				Data: map[string]string{
					//"filter": filter,
					"error": err.Error(),
				},
			})
			return nil, err
		}

		logger.Info(logger.Format{
			Message: "Successfully deleted existing menu and items",
			Data:    map[string]string{
				//"provider_restaurant_id": restaurantId,
			},
		})

		menu["provider"] = provider

		// 2. Insert menu
		_, err = posMenuCollection.InsertOne(sessCtx, menu)
		if err != nil {
			logger.Error(logger.Format{
				Message: "Failed to insert new menu",
				Data: map[string]string{
					//"provider_restaurant_id": restaurantId,
					"error": err.Error(),
				},
			})
			return nil, err
		}

		logger.Info(logger.Format{
			Message: "Successfully inserted new menu",
			Data:    map[string]string{
				//"provider_restaurant_id": restaurantId,
			},
		})

		if err != nil {
			logger.Error(logger.Format{
				Message: "Transaction failed",
				Data: map[string]string{
					"error": err.Error(),
				},
			})
		}
		logger.Info(logger.Format{
			Message: "Successfully completed menu push operation",
			Data:    map[string]string{
				//"provider_restaurant_id": restaurantId,
			},
		})
		return nil, nil
	})
	return nil
}

func (m mongoRepositoryImpl) PushPOSOrder(filter bson.M, order map[string]interface{}, provider string) error {
	// Create a context with timeout
	timeout := time.Duration(30) * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	// Start a session for the transaction
	session, err := m.mongoClient.Client().StartSession()
	if err != nil {
		logger.Error(logger.Format{
			Message: "Failed to start MongoDB session for order",
			Data: map[string]string{
				"error": err.Error(),
			},
		})
		return err
	}
	defer session.EndSession(ctx)

	// Execute the transaction
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
		// Get order collection
		posOrderCollection := m.mongoClient.Collection(constants.POSOrderCollection)

		// Add provider and timestamp to order
		order["provider"] = provider
		order["created_at"] = time.Now()
		order["updated_at"] = time.Now()

		// Insert order (we don't delete existing orders, just insert new ones)
		_, err := posOrderCollection.InsertOne(sessCtx, order)
		if err != nil {
			logger.Error(logger.Format{
				Message: "Failed to insert new order",
				Data: map[string]string{
					"error": err.Error(),
				},
			})
			return nil, err
		}

		logger.Info(logger.Format{
			Message: "Successfully inserted new order",
			Data: map[string]string{
				"provider": provider,
			},
		})

		return nil, nil
	})

	if err != nil {
		logger.Error(logger.Format{
			Message: "Order transaction failed",
			Data: map[string]string{
				"error": err.Error(),
			},
		})
		return err
	}

	logger.Info(logger.Format{
		Message: "Successfully completed order push operation",
		Data: map[string]string{
			"provider": provider,
		},
	})

	return nil
}